// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema", "postgresqlExtensions", "strictUndefinedChecks"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  schemas    = ["public", "extensions"]
  extensions = [pgvector(map: "vector", schema: "extensions")]
}

/// Represents an application that "owns" the connectors.
model Application {
  id          String   @id @default(uuid()) @db.Uuid
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  versions ApplicationVersion[]

  @@unique([name])
  @@schema("public")
}

/// Represents a version of an application.
model ApplicationVersion {
  id            String      @id @default(uuid()) @db.Uuid
  applicationId String      @db.Uuid
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  version       Int         @default(0)
  createdAt     DateTime    @default(now())

  connectors ApplicationConnector[]

  @@unique([applicationId, version])
  @@schema("public")
}

/// Represents a single connector. Every connector must be related to an application version.
model ApplicationConnector {
  id          String             @id @default(uuid()) @db.Uuid
  versionId   String             @db.Uuid
  version     ApplicationVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)
  name        String
  description String?
  createdAt   DateTime           @default(now())

  indices ApplicationConnectorIndex[]

  @@unique([versionId, name])
  @@schema("public")
}

/// A semantic index for a connector. Each connector can have multiple indices for better search.
model ApplicationConnectorIndex {
  id          String                                 @id @default(uuid()) @db.Uuid
  connectorId String                                 @db.Uuid
  connector   ApplicationConnector                   @relation(fields: [connectorId], references: [id], onDelete: Cascade)
  query       String
  /// Cohere embedding "embed-multilingual-light-v3.0"
  ///
  /// See: https://docs.cohere.com/reference/embed
  embedding   Unsupported("extensions.halfvec(384)")

  @@index([embedding], map: "ConnectorIndex_embedding_idx")
  @@schema("public")
}
