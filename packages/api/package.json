{"name": "@wrtnlabs/connector-hive-api", "version": "1.5.0", "description": "connector-hive api", "main": "lib/index.js", "module": "lib/index.mjs", "typings": "lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/wrtnlabs/connector-hive"}, "author": "Shrimp <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wrtnlabs/connector-hive/issues"}, "homepage": "https://github.com/wrtnlabs/connector-hive", "files": ["lib", "package.json", "swagger.json", "README.md"], "dependencies": {"@nestia/fetcher": "^6.0.1", "tgrid": "^1.1.0", "typia": "^9.0.1"}}