/**
 * @packageDocumentation
 * @module api.functional.connectors.by_names.all_versions
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
//================================================================
import type { IConnection } from "@nestia/fetcher";
import { NestiaSimulator } from "@nestia/fetcher/lib/NestiaSimulator";
import { PlainFetcher } from "@nestia/fetcher/lib/PlainFetcher";
import typia from "typia";

import type { IApplicationConnector } from "../../../../structures/connector/IApplicationConnector";

/**
 * List all connectors for a given name across all versions.
 *
 * @param name - Name of the connector.
 * @returns List of connectors.
 * @tag connector
 *
 * @controller ApplicationConnectorController.listAllVersions
 * @path GET /connectors/by-names/:name/all-versions
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function listAllVersions(
  connection: IConnection,
  name: string,
  query: IApplicationConnector.IListQueryAllVersions,
): Promise<listAllVersions.Output> {
  return !!connection.simulate
    ? listAllVersions.simulate(connection, name, query)
    : PlainFetcher.fetch(connection, {
        ...listAllVersions.METADATA,
        template: listAllVersions.METADATA.path,
        path: listAllVersions.path(name, query),
      });
}
export namespace listAllVersions {
  export type Query = IApplicationConnector.IListQueryAllVersions;
  export type Output = Array<IApplicationConnector>;

  export const METADATA = {
    method: "GET",
    path: "/connectors/by-names/:name/all-versions",
    request: null,
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (name: string, query: listAllVersions.Query) => {
    const variables: URLSearchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(query as any))
      if (undefined === value) continue;
      else if (Array.isArray(value))
        value.forEach((elem: any) => variables.append(key, String(elem)));
      else variables.set(key, String(value));
    const location: string = `/connectors/by-names/${encodeURIComponent(name?.toString() ?? "null")}/all-versions`;
    return 0 === variables.size
      ? location
      : `${location}?${variables.toString()}`;
  };
  export const random = (
    g?: Partial<typia.IRandomGenerator>,
  ): Array<IApplicationConnector> =>
    typia.random<Array<IApplicationConnector>>(g);
  export const simulate = (
    connection: IConnection,
    name: string,
    query: IApplicationConnector.IListQueryAllVersions,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(name, query),
      contentType: "application/json",
    });
    assert.param("name")(() => typia.assert(name));
    assert.query(() => typia.assert(query));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}
