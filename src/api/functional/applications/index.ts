/**
 * @packageDocumentation
 * @module api.functional.applications
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
//================================================================
import type { IConnection } from "@nestia/fetcher";
import { NestiaSimulator } from "@nestia/fetcher/lib/NestiaSimulator";
import { PlainFetcher } from "@nestia/fetcher/lib/PlainFetcher";
import typia from "typia";

import type { IApplication } from "../../structures/connector/IApplication";

export * as by_ids from "./by_ids";
export * as by_names from "./by_names";

/**
 * List all applications.
 *
 * List all applications, sorted by name in ascending alphabetical order.
 * This endpoint uses cursor-based pagination.
 *
 * @param query - Query parameters.
 * @returns List of applications.
 * @tag application
 *
 * @controller ApplicationController.list
 * @path GET /applications
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function list(
  connection: IConnection,
  query: IApplication.IListQuery,
): Promise<list.Output> {
  return !!connection.simulate
    ? list.simulate(connection, query)
    : PlainFetcher.fetch(connection, {
        ...list.METADATA,
        template: list.METADATA.path,
        path: list.path(query),
      });
}
export namespace list {
  export type Query = IApplication.IListQuery;
  export type Output = Array<IApplication>;

  export const METADATA = {
    method: "GET",
    path: "/applications",
    request: null,
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (query: list.Query) => {
    const variables: URLSearchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(query as any))
      if (undefined === value) continue;
      else if (Array.isArray(value))
        value.forEach((elem: any) => variables.append(key, String(elem)));
      else variables.set(key, String(value));
    const location: string = "/applications";
    return 0 === variables.size
      ? location
      : `${location}?${variables.toString()}`;
  };
  export const random = (
    g?: Partial<typia.IRandomGenerator>,
  ): Array<IApplication> => typia.random<Array<IApplication>>(g);
  export const simulate = (
    connection: IConnection,
    query: IApplication.IListQuery,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(query),
      contentType: "application/json",
    });
    assert.query(() => typia.assert(query));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}

/**
 * Create a new application.
 *
 * @param body - Application to create.
 * @returns Created application.
 * @tag application
 *
 * @controller ApplicationController.create
 * @path POST /applications
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function create(
  connection: IConnection,
  body: IApplication.ICreate,
): Promise<create.Output> {
  return !!connection.simulate
    ? create.simulate(connection, body)
    : PlainFetcher.fetch(
        {
          ...connection,
          headers: {
            ...connection.headers,
            "Content-Type": "application/json",
          },
        },
        {
          ...create.METADATA,
          template: create.METADATA.path,
          path: create.path(),
        },
        body,
      );
}
export namespace create {
  export type Input = IApplication.ICreate;
  export type Output = IApplication;

  export const METADATA = {
    method: "POST",
    path: "/applications",
    request: {
      type: "application/json",
      encrypted: false,
    },
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 201,
  } as const;

  export const path = () => "/applications";
  export const random = (g?: Partial<typia.IRandomGenerator>): IApplication =>
    typia.random<IApplication>(g);
  export const simulate = (
    connection: IConnection,
    body: IApplication.ICreate,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(),
      contentType: "application/json",
    });
    assert.body(() => typia.assert(body));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}
