/**
 * @packageDocumentation
 * @module api.functional.applications.by_ids
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
//================================================================
import type { IConnection } from "@nestia/fetcher";
import { NestiaSimulator } from "@nestia/fetcher/lib/NestiaSimulator";
import { PlainFetcher } from "@nestia/fetcher/lib/PlainFetcher";
import typia from "typia";
import type { Format } from "typia/lib/tags/Format";

import type { IApplication } from "../../../structures/connector/IApplication";

export * as versions from "./versions";

/**
 * Get an application by its ID.
 *
 * @param id - ID of the application.
 * @returns Application.
 * @tag application
 *
 * @controller ApplicationController.getById
 * @path GET /applications/by-ids/:id
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function getById(
  connection: IConnection,
  id: string & Format<"uuid">,
): Promise<getById.Output> {
  return !!connection.simulate
    ? getById.simulate(connection, id)
    : PlainFetcher.fetch(connection, {
        ...getById.METADATA,
        template: getById.METADATA.path,
        path: getById.path(id),
      });
}
export namespace getById {
  export type Output = IApplication;

  export const METADATA = {
    method: "GET",
    path: "/applications/by-ids/:id",
    request: null,
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (id: string & Format<"uuid">) =>
    `/applications/by-ids/${encodeURIComponent(id?.toString() ?? "null")}`;
  export const random = (g?: Partial<typia.IRandomGenerator>): IApplication =>
    typia.random<IApplication>(g);
  export const simulate = (
    connection: IConnection,
    id: string & Format<"uuid">,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(id),
      contentType: "application/json",
    });
    assert.param("id")(() => typia.assert(id));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}

/**
 * Update an application.
 *
 * @param id - ID of the application to update.
 * @param body - Application to update.
 * @returns Updated application.
 * @tag application
 *
 * @controller ApplicationController.update
 * @path PUT /applications/by-ids/:id
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function update(
  connection: IConnection,
  id: string & Format<"uuid">,
  body: IApplication.IUpdate,
): Promise<update.Output> {
  return !!connection.simulate
    ? update.simulate(connection, id, body)
    : PlainFetcher.fetch(
        {
          ...connection,
          headers: {
            ...connection.headers,
            "Content-Type": "application/json",
          },
        },
        {
          ...update.METADATA,
          template: update.METADATA.path,
          path: update.path(id),
        },
        body,
      );
}
export namespace update {
  export type Input = IApplication.IUpdate;
  export type Output = IApplication;

  export const METADATA = {
    method: "PUT",
    path: "/applications/by-ids/:id",
    request: {
      type: "application/json",
      encrypted: false,
    },
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (id: string & Format<"uuid">) =>
    `/applications/by-ids/${encodeURIComponent(id?.toString() ?? "null")}`;
  export const random = (g?: Partial<typia.IRandomGenerator>): IApplication =>
    typia.random<IApplication>(g);
  export const simulate = (
    connection: IConnection,
    id: string & Format<"uuid">,
    body: IApplication.IUpdate,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(id),
      contentType: "application/json",
    });
    assert.param("id")(() => typia.assert(id));
    assert.body(() => typia.assert(body));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}

/**
 * Delete an application.
 *
 * @param id - ID of the application to delete.
 * @tag application
 *
 * @controller ApplicationController.remove
 * @path DELETE /applications/by-ids/:id
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function remove(
  connection: IConnection,
  id: string & Format<"uuid">,
): Promise<void> {
  return !!connection.simulate
    ? remove.simulate(connection, id)
    : PlainFetcher.fetch(connection, {
        ...remove.METADATA,
        template: remove.METADATA.path,
        path: remove.path(id),
      });
}
export namespace remove {
  export const METADATA = {
    method: "DELETE",
    path: "/applications/by-ids/:id",
    request: null,
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (id: string & Format<"uuid">) =>
    `/applications/by-ids/${encodeURIComponent(id?.toString() ?? "null")}`;
  export const random = (g?: Partial<typia.IRandomGenerator>): void =>
    typia.random<void>(g);
  export const simulate = (
    connection: IConnection,
    id: string & Format<"uuid">,
  ): void => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(id),
      contentType: "application/json",
    });
    assert.param("id")(() => typia.assert(id));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}
