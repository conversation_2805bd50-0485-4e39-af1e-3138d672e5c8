/**
 * @packageDocumentation
 * @module api.functional.applications.by_ids.versions.latest
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
//================================================================
import type { IConnection } from "@nestia/fetcher";
import { NestiaSimulator } from "@nestia/fetcher/lib/NestiaSimulator";
import { PlainFetcher } from "@nestia/fetcher/lib/PlainFetcher";
import typia from "typia";
import type { Format } from "typia/lib/tags/Format";

import type { IApplicationVersion } from "../../../../../structures/connector/IApplicationVersion";

/**
 * Get the latest version of an application.
 *
 * @param id - ID of the application.
 * @returns Latest application version.
 * @tag application-version
 *
 * @controller ApplicationVersionController.getLatest
 * @path GET /applications/by-ids/:id/versions/latest
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function getLatest(
  connection: IConnection,
  id: string & Format<"uuid">,
): Promise<getLatest.Output> {
  return !!connection.simulate
    ? getLatest.simulate(connection, id)
    : PlainFetcher.fetch(connection, {
        ...getLatest.METADATA,
        template: getLatest.METADATA.path,
        path: getLatest.path(id),
      });
}
export namespace getLatest {
  export type Output = IApplicationVersion;

  export const METADATA = {
    method: "GET",
    path: "/applications/by-ids/:id/versions/latest",
    request: null,
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (id: string & Format<"uuid">) =>
    `/applications/by-ids/${encodeURIComponent(id?.toString() ?? "null")}/versions/latest`;
  export const random = (
    g?: Partial<typia.IRandomGenerator>,
  ): IApplicationVersion => typia.random<IApplicationVersion>(g);
  export const simulate = (
    connection: IConnection,
    id: string & Format<"uuid">,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(id),
      contentType: "application/json",
    });
    assert.param("id")(() => typia.assert(id));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}
