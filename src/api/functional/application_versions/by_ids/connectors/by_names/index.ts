/**
 * @packageDocumentation
 * @module api.functional.application_versions.by_ids.connectors.by_names
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
//================================================================
import type { IConnection } from "@nestia/fetcher";
import { NestiaSimulator } from "@nestia/fetcher/lib/NestiaSimulator";
import { PlainFetcher } from "@nestia/fetcher/lib/PlainFetcher";
import typia from "typia";
import type { Format } from "typia/lib/tags/Format";

import type { IApplicationConnector } from "../../../../../structures/connector/IApplicationConnector";

/**
 * Get a connector by its name.
 *
 * @param id - ID of the application version.
 * @param name - Name of the connector.
 * @returns Connector.
 * @tag connector
 *
 * @controller ApplicationConnectorController.getByName
 * @path GET /application-versions/by-ids/:id/connectors/by-names/:name
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function getByName(
  connection: IConnection,
  id: string & Format<"uuid">,
  name: string,
): Promise<getByName.Output> {
  return !!connection.simulate
    ? getByName.simulate(connection, id, name)
    : PlainFetcher.fetch(connection, {
        ...getByName.METADATA,
        template: getByName.METADATA.path,
        path: getByName.path(id, name),
      });
}
export namespace getByName {
  export type Output = IApplicationConnector;

  export const METADATA = {
    method: "GET",
    path: "/application-versions/by-ids/:id/connectors/by-names/:name",
    request: null,
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 200,
  } as const;

  export const path = (id: string & Format<"uuid">, name: string) =>
    `/application-versions/by-ids/${encodeURIComponent(id?.toString() ?? "null")}/connectors/by-names/${encodeURIComponent(name?.toString() ?? "null")}`;
  export const random = (
    g?: Partial<typia.IRandomGenerator>,
  ): IApplicationConnector => typia.random<IApplicationConnector>(g);
  export const simulate = (
    connection: IConnection,
    id: string & Format<"uuid">,
    name: string,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(id, name),
      contentType: "application/json",
    });
    assert.param("id")(() => typia.assert(id));
    assert.param("name")(() => typia.assert(name));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}
