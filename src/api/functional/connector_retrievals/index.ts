/**
 * @packageDocumentation
 * @module api.functional.connector_retrievals
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
//================================================================
import type { IConnection } from "@nestia/fetcher";
import { NestiaSimulator } from "@nestia/fetcher/lib/NestiaSimulator";
import { PlainFetcher } from "@nestia/fetcher/lib/PlainFetcher";
import typia from "typia";

import type { IApplicationConnectorRetrieval } from "../../structures/connector/IApplicationConnectorRetrieval";

/**
 * Perform connector retrieval.
 *
 * It searches for connectors that match the query semantically,
 * using embeddings, and returns them sorted by relevance in descending order (most relevant first).
 *
 * @param body - Connector retrieval request, including the query, limit, and optional filters.
 * @returns List of connectors, sorted by semantic similarity to the query. Each connector contains a similarity value,
 *          which represents the semantic similarity between the query and the connector.
 * @tag connector
 *
 * @controller ApplicationConnectorController.createRetrievalRequest
 * @path POST /connector-retrievals
 * @nestia Generated by Nestia - https://github.com/samchon/nestia
 */
export async function createRetrievalRequest(
  connection: IConnection,
  body: IApplicationConnectorRetrieval.ICreate,
): Promise<createRetrievalRequest.Output> {
  return !!connection.simulate
    ? createRetrievalRequest.simulate(connection, body)
    : PlainFetcher.fetch(
        {
          ...connection,
          headers: {
            ...connection.headers,
            "Content-Type": "application/json",
          },
        },
        {
          ...createRetrievalRequest.METADATA,
          template: createRetrievalRequest.METADATA.path,
          path: createRetrievalRequest.path(),
        },
        body,
      );
}
export namespace createRetrievalRequest {
  export type Input = IApplicationConnectorRetrieval.ICreate;
  export type Output =
    Array<IApplicationConnectorRetrieval.IRetrievedConnector>;

  export const METADATA = {
    method: "POST",
    path: "/connector-retrievals",
    request: {
      type: "application/json",
      encrypted: false,
    },
    response: {
      type: "application/json",
      encrypted: false,
    },
    status: 201,
  } as const;

  export const path = () => "/connector-retrievals";
  export const random = (
    g?: Partial<typia.IRandomGenerator>,
  ): Array<IApplicationConnectorRetrieval.IRetrievedConnector> =>
    typia.random<Array<IApplicationConnectorRetrieval.IRetrievedConnector>>(g);
  export const simulate = (
    connection: IConnection,
    body: IApplicationConnectorRetrieval.ICreate,
  ): Output => {
    const assert = NestiaSimulator.assert({
      method: METADATA.method,
      host: connection.host,
      path: path(),
      contentType: "application/json",
    });
    assert.body(() => typia.assert(body));
    return random(
      "object" === typeof connection.simulate && null !== connection.simulate
        ? connection.simulate
        : undefined,
    );
  };
}
