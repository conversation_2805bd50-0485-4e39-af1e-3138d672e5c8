{
  // Use IntelliSense to learn about possible Node.js debug attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
  {
    "type": "node",
    "request": "launch",
    "name": "Backend Test",
    "program": "${workspaceRoot}/test/index.ts",
    "cwd": "${workspaceRoot}",
    "args": [
      // //----
      // // Not possible to reset DB in debugging mode
      // //
      // // Therefore, if you need DB reset, then do it 
      // // through `npm run reset-for-debugging` command
      // //----
      // "--reset", "false",
      // "--mode", "local",
      
      //----
      // You can run specific test functions
      //
      // If you want to include or exclude multiple words,
      // then separate them with space character
      //----
      // "--include", "some-words-to-include",
      // "--exclude", "some-word another-word",
  ],
    "outFiles": ["${workspaceRoot}/bin/**/*.js"],
  }
]
}