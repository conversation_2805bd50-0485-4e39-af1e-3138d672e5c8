name: build
on:
  pull_request:
    paths:
      - "src/**"
      - "test/**"
      - "package.json"
jobs:
  Ubuntu:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Install Backend-Server
        run: npm install

      - name: Build Env
        run: npm run build:env

      - name: Build Swagger
        run: npm run build:swagger

      - name: Build SDK
        run: npm run build:sdk

      - name: Compile Backend-Server
        run: npm run build

      - name: Run Test Program
        run: npm run test

      - name: Test Webpack
        run: npm run webpack && npm run test:webpack

      - name: EsLint
        run: npm run eslint
