name: Client SDK Release to NPM

on:
  release:
    types: [created]
jobs:
  publish:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: none
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: https://registry.npmjs.org/
      - name: Install dependencies
        run: npm install
      - name: Build Env File
        run: npm run build:env
      - name: SDK Build
        run: npm run build:api
      - name: Publish SDK to NPM
        run: npm publish --access public --provenance
        working-directory: ./packages/api
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_PUBLISH }}
