{"name": "@wrtnlabs/connector-hive", "version": "1.5.0", "description": "connector retrieval server", "main": "lib/index.js", "scripts": {"benchmark": "node bin/test/benchmark", "test": "node bin/test", "test:webpack": "npm run webpack && node bin/test/webpack.js", "------------------------BUILDS------------------------": "", "build": "npm run build:sdk && npm run build:main && npm run build:test", "build:api": "rimraf packages/api/lib && nestia all && rimraf packages/api/lib && tsc -p packages/api/tsconfig.json && rollup -c packages/api/rollup.config.js", "build:env": "ts-node build/env.ts", "build:main": "rimraf lib && tsc", "build:sdk": "rimraf src/api/functional && nestia sdk", "build:swagger": "npx nestia swagger", "build:test": "rimraf bin && tsc -p test/tsconfig.json", "dev": "npm run build:test -- --watch", "eslint": "eslint src && eslint test", "eslint:fix": "eslint --fix src && eslint --fix test", "prepare": "ts-patch install && typia patch", "prettier": "prettier src --write && prettier test --write", "------------------------WEBPACK------------------------": "", "webpack": "rimraf dist && webpack", "webpack:start": "cd dist && node server", "webpack:test": "npm run webpack && node bin/test/webpack.js", "------------------------DEPLOYS------------------------": "", "package:api": "npm run build:api && cd packages/api && npm publish", "start": "node lib/executables/server", "start:swagger": "ts-node src/executables/swagger.ts"}, "repository": {"type": "git", "url": "https://github.com/wrtnlabs/connector-hive"}, "author": "Shrimp <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wrtnlabs/connector-hive/issues"}, "homepage": "https://github.com/wrtnlabs/connector-hive#readme", "devDependencies": {"@nestia/benchmark": "^6.0.1", "@nestia/e2e": "^6.0.1", "@nestia/sdk": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/cli": "^0.11.21", "@types/cli-progress": "^3.11.5", "@types/express": "^5.0.0", "@types/node": "^22.13.4", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "chalk": "^5.4.1", "cli": "^1.0.1", "cli-progress": "^3.12.0", "copy-webpack-plugin": "^12.0.2", "eslint-plugin-deprecation": "^3.0.0", "express": "^4.18.2", "nestia": "^7.0.0", "prettier": "^3.2.4", "prettier-plugin-prisma": "^5.0.0", "prisma": "^6.4.0", "rimraf": "^6.0.1", "rollup": "^4.18.0", "source-map-support": "^0.5.21", "swagger-ui-express": "^5.0.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "ts-patch": "^3.3.0", "typescript": "~5.7.2", "typescript-transform-paths": "^3.5.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1", "write-file-webpack-plugin": "^4.5.1"}, "dependencies": {"@inquirer/prompts": "^7.3.2", "@nestia/core": "^6.0.1", "@nestia/fetcher": "^6.0.1", "@nestjs/common": "^11.0.9", "@nestjs/core": "^11.0.9", "@nestjs/platform-express": "^11.0.9", "@prisma/client": "^6.4.0", "bufferutil": "^4.0.9", "cohere-ai": "^7.15.4", "commander": "^13.1.0", "dotenv": "^16.3.1", "dotenv-expand": "^12.0.1", "nestjs-pino": "^4.3.0", "serialize-error": "^12.0.0", "tgrid": "^1.0.2", "tstl": "^3.0.0", "typia": "^9.0.1", "utf-8-validate": "^6.0.5", "uuid": "^11.0.5"}, "stackblitz": {"startCommand": "npm run prepare && npm run build:test && npm run test"}}